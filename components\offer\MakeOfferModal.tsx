import React, { useState, useEffect } from 'react';
import {
  View,
  Modal,
  TouchableOpacity,
  TouchableWithoutFeedback,
  TextInput,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Animated,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { Ionicons } from '@expo/vector-icons';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING, BORDERS, FONTS, SHADOWS } from '@/constants/theme';
import { AppDispatch, RootState } from '@/store/store';
import { makeOffer, clearOfferState } from '@/store/slices/offerSlice';

interface MakeOfferModalProps {
  visible: boolean;
  onClose: () => void;
  vehicleId: number;
  vehicleType: string;
  vehiclePrice: number;
  onOfferSuccess?: () => void;
}

export default function MakeOfferModal({
  visible,
  onClose,
  vehicleId,
  vehicleType,
  vehiclePrice,
  onOfferSuccess,
}: MakeOfferModalProps) {
  const dispatch = useDispatch<AppDispatch>();
  const { submitting, submitSuccess, error } = useSelector(
    (state: RootState) => state.offer
  );

  const [offerAmount, setOfferAmount] = useState('');
  const [slideAnim] = useState(new Animated.Value(300));
  const [backdropOpacity] = useState(new Animated.Value(0));
  const [isAlertVisible, setIsAlertVisible] = useState(false);

  // Calculate minimum offer (20% of vehicle price)
  const minimumOffer = Math.ceil(vehiclePrice * 0.2);
  const offerRange = `₹ ${minimumOffer.toLocaleString('en-IN')} - ₹ ${vehiclePrice.toLocaleString('en-IN')}`;

  useEffect(() => {
    if (visible) {
      // Animate modal in
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Reset form when modal closes
      setOfferAmount('');
      dispatch(clearOfferState());
    }
  }, [visible]);

  useEffect(() => {
    if (submitSuccess) {
      setIsAlertVisible(true);
      // Delay alert to ensure modal animation completes
      setTimeout(() => {
        Alert.alert(
          'Success',
          'Your offer has been submitted successfully!',
          [
            {
              text: 'OK',
              onPress: () => {
                setIsAlertVisible(false);
                onOfferSuccess?.();
                handleClose();
              },
            },
          ],
          { cancelable: false }
        );
      }, 100);
    }
  }, [submitSuccess]);

  useEffect(() => {
    if (error) {
      setIsAlertVisible(true);
      // Delay alert to ensure modal animation completes
      setTimeout(() => {
        Alert.alert(
          'Error',
          error,
          [
            {
              text: 'OK',
              onPress: () => {
                setIsAlertVisible(false);
                dispatch(clearOfferState());
              },
            },
          ],
          { cancelable: false }
        );
      }, 100);
    }
  }, [error]);

  const handleClose = () => {
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 300,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacity, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  const validateOffer = (amount: string): Promise<boolean> => {
    return new Promise((resolve) => {
      const numericAmount = parseInt(amount.replace(/,/g, ''));

      if (isNaN(numericAmount) || numericAmount <= 0) {
        setIsAlertVisible(true);
        Alert.alert(
          'Invalid Amount',
          'Please enter a valid offer amount.',
          [
            {
              text: 'OK',
              onPress: () => {
                setIsAlertVisible(false);
                resolve(false);
              },
            },
          ],
          { cancelable: false }
        );
        return;
      }

      if (numericAmount < minimumOffer) {
        setIsAlertVisible(true);
        Alert.alert(
          'Minimum Offer Required',
          `The minimum offer amount is ₹ ${minimumOffer.toLocaleString('en-IN')} (20% of the vehicle price).`,
          [
            {
              text: 'OK',
              onPress: () => {
                setIsAlertVisible(false);
                resolve(false);
              },
            },
          ],
          { cancelable: false }
        );
        return;
      }

      if (numericAmount > vehiclePrice) {
        setIsAlertVisible(true);
        Alert.alert(
          'Offer Too High',
          `Your offer cannot exceed the vehicle price of ₹ ${vehiclePrice.toLocaleString('en-IN')}.`,
          [
            {
              text: 'OK',
              onPress: () => {
                setIsAlertVisible(false);
                resolve(false);
              },
            },
          ],
          { cancelable: false }
        );
        return;
      }

      resolve(true);
    });
  };

  const handleSubmitOffer = async () => {
    // Prevent submission if alert is visible
    if (isAlertVisible) {
      return;
    }

    const isValid = await validateOffer(offerAmount);
    if (!isValid) {
      return;
    }

    const numericAmount = parseInt(offerAmount.replace(/,/g, ''));

    dispatch(makeOffer({
      id: vehicleId,
      type: vehicleType,
      make_offer: numericAmount,
    }));
  };

  const formatAmount = (text: string) => {
    // Remove non-numeric characters
    const numericValue = text.replace(/[^0-9]/g, '');

    // Format with commas
    if (numericValue) {
      const formatted = parseInt(numericValue).toLocaleString('en-IN');
      setOfferAmount(formatted);
    } else {
      setOfferAmount('');
    }
  };

  const isValidOffer = () => {
    const numericAmount = parseInt(offerAmount.replace(/,/g, ''));
    return !isNaN(numericAmount) && numericAmount >= minimumOffer && numericAmount <= vehiclePrice;
  };

  return (
    <Modal
      visible={visible && !isAlertVisible}
      transparent={true}
      animationType="none"
      onRequestClose={handleClose}
      statusBarTranslucent={true}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.container}
      >
        <TouchableWithoutFeedback onPress={handleClose}>
          <Animated.View
            style={[styles.backdrop, { opacity: backdropOpacity }]}
          />
        </TouchableWithoutFeedback>

        <Animated.View
          style={[
            styles.modalContent,
            { transform: [{ translateY: slideAnim }] },
          ]}
        >
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Ionicons name="close" size={24} color={COLORS.text.secondary} />
          </TouchableOpacity>

          <ThemedText style={styles.title}>Place Offer</ThemedText>

          <ThemedText style={styles.rangeText}>
            Offer Range: {offerRange}
          </ThemedText>

          <View style={styles.inputContainer}>
            <ThemedText style={styles.inputLabel}>Enter Offer Amount</ThemedText>
            <View style={styles.inputWrapper}>
              <ThemedText style={styles.currencySymbol}>₹</ThemedText>
              <TextInput
                style={styles.input}
                value={offerAmount}
                onChangeText={formatAmount}
                placeholder="Enter Amount"
                placeholderTextColor={COLORS.text.secondary}
                keyboardType="numeric"
                maxLength={15}
                editable={!submitting}
              />
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.submitButton,
              (!isValidOffer() || submitting || isAlertVisible) && styles.submitButtonDisabled,
            ]}
            onPress={handleSubmitOffer}
            disabled={!isValidOffer() || submitting || isAlertVisible}
          >
            {submitting ? (
              <ActivityIndicator size="small" color={COLORS.white} />
            ) : (
              <ThemedText style={styles.submitButtonText}>Continue</ThemedText>
            )}
          </TouchableOpacity>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
  },
  modalContent: {
    backgroundColor: COLORS.white,
    borderTopLeftRadius: BORDERS.radius.lg,
    borderTopRightRadius: BORDERS.radius.lg,
    padding: SPACING.lg,
    paddingBottom: SPACING.xl,
    minHeight: 300,
    zIndex: 1001,
    ...SHADOWS.md,
  },
  closeButton: {
    position: 'absolute',
    top: SPACING.md,
    right: SPACING.md,
    padding: SPACING.xs,
    zIndex: 1,
  },
  title: {
    fontSize: FONTS.size.h2,
    fontWeight: FONTS.weight.bold,
    color: COLORS.text.primary,
    textAlign: 'center',
    marginBottom: SPACING.sm,
    marginTop: SPACING.md,
  },
  rangeText: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.secondary,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  inputContainer: {
    marginBottom: SPACING.xl,
  },
  inputLabel: {
    fontSize: FONTS.size.caption,
    color: COLORS.text.primary,
    marginBottom: SPACING.xs,
    fontWeight: FONTS.weight.medium,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: BORDERS.width.thin,
    borderColor: COLORS.border.primary,
    borderRadius: BORDERS.radius.sm,
    backgroundColor: COLORS.white,
    paddingHorizontal: SPACING.md,
    minHeight: 56,
  },
  currencySymbol: {
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    marginRight: SPACING.xs,
    fontWeight: FONTS.weight.medium,
  },
  input: {
    flex: 1,
    fontSize: FONTS.size.body,
    color: COLORS.text.primary,
    paddingVertical: SPACING.md,
  },
  submitButton: {
    backgroundColor: COLORS.primary,
    borderRadius: BORDERS.radius.sm,
    paddingVertical: SPACING.md,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 56,
    ...SHADOWS.sm,
  },
  submitButtonDisabled: {
    backgroundColor: COLORS.gray[400],
    opacity: 0.6,
  },
  submitButtonText: {
    color: COLORS.white,
    fontSize: FONTS.size.body,
    fontWeight: FONTS.weight.bold,
  },
});
