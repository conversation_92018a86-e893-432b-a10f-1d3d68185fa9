import React, { useEffect, useState } from 'react';
import {
    View,
    ScrollView,
    Image,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
    Linking,
    Alert,
    Platform,
    Share
} from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { useLocalSearchParams, router } from 'expo-router';
import { AppDispatch, RootState } from '@/store/store';
import { fetchVehicleDetail } from '@/store/slices/vehicleDetailSlice';
import { ThemedText } from '@/components/common/ThemedText';
import { COLORS, SPACING } from '@/constants/theme';
import LoadingIndicator from '@/components/common/LoadingIndicator';
import { Ionicons } from '@expo/vector-icons';
import { VehicleType } from '@/store/slices/vehicleSlice';
import Constants from 'expo-constants';
import * as Clipboard from 'expo-clipboard';
import { INSURANCE_OPTIONS } from '@/constants/commercialVehicle';
import MakeOfferModal from '@/components/offer/MakeOfferModal';
import OffersList from '@/components/offer/OffersList';
import { resetOffers } from '@/store/slices/offerSlice';

const { width } = Dimensions.get('window');

// Helper function to get insurance type display name
const getInsuranceDisplayName = (insuranceType: string) => {
    const option = INSURANCE_OPTIONS.find(opt => opt.key === insuranceType);
    return option ? option.name : insuranceType;
};

export default function VehicleDetailScreen() {
    const { id, type } = useLocalSearchParams<{ id: string; type: string }>();
    const dispatch = useDispatch<AppDispatch>();
    const { vehicle, loading, error, offers } = useSelector(
        (state: RootState) => state.vehicleDetail
    );
    const { selectedVehicleType } = useSelector((state: RootState) => state.vehicles);
    const { submitSuccess } = useSelector((state: RootState) => state.offer);
    const [selectedImage, setSelectedImage] = useState<string>('');
    const [isOverviewExpanded, setIsOverviewExpanded] = useState(true);
    const [isDescriptionExpanded, setIsDescriptionExpanded] = useState(true);
    const [makeOfferModalVisible, setMakeOfferModalVisible] = useState(false);

    useEffect(() => {
        if (id && type) {
            dispatch(fetchVehicleDetail({ id: parseInt(id), type: type as VehicleType }));
        }
    }, [id, type, dispatch]);

    useEffect(() => {
        if (vehicle?.primary_image) {
            setSelectedImage(vehicle.primary_image);
        }
    }, [vehicle]);

    // Reset offers when component unmounts
    useEffect(() => {
        return () => {
            dispatch(resetOffers());
        };
    }, [dispatch]);

    const handleChatPress = () => {
        if (!vehicle?.user?.phone) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        // Clean phone number (remove any non-numeric characters)
        const cleanPhoneNumber = vehicle.user.phone.replace(/\D/g, '');

        // Make sure we have a valid phone number
        if (!cleanPhoneNumber || cleanPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        const whatsappMessage = `Hi, I am interested in your ${vehicle?.title} listed on 2ndCar. Is it still available?`;
        const whatsappUrl = `whatsapp://send?phone=91${cleanPhoneNumber}&text=${encodeURIComponent(whatsappMessage)}`;

        Linking.canOpenURL(whatsappUrl)
            .then(supported => {
                if (supported) {
                    return Linking.openURL(whatsappUrl);
                } else {
                    // Try web URL as fallback
                    const webWhatsappUrl = `https://wa.me/91${cleanPhoneNumber}?text=${encodeURIComponent(whatsappMessage)}`;
                    return Linking.openURL(webWhatsappUrl)
                        .catch(() => {
                            throw new Error('WhatsApp is not installed on your device');
                        });
                }
            })
            .catch(error => {
                console.error('Error opening WhatsApp:', error);
                Alert.alert(
                    'Error',
                    'Could not open WhatsApp. Please make sure WhatsApp is installed on your device.',
                    [{ text: 'OK' }]
                );
            });
    };

    const handleCallPress = () => {
        const phoneNumber = vehicle?.user?.phone?.trim();

        if (!phoneNumber) {
            Alert.alert('Error', 'Phone number not available');
            return;
        }

        // iOS simulator check using Expo Constants
        if (Platform.OS === 'ios' && !Constants.isDevice) {
            Alert.alert('Error', 'Phone calls are not supported on the iOS simulator.');
            return;
        }

        // Clean the phone number:
        // Preserve the leading '+' if it exists, else remove all non-numeric characters.
        const cleanedPhoneNumber = phoneNumber.startsWith('+')
            ? '+' + phoneNumber.slice(1).replace(/\D/g, '')
            : phoneNumber.replace(/\D/g, '');

        // Validate: Exclude '+' from the digit count
        const numericPhoneNumber = cleanedPhoneNumber.startsWith('+')
            ? cleanedPhoneNumber.slice(1)
            : cleanedPhoneNumber;
        if (numericPhoneNumber.length < 10) {
            Alert.alert('Error', 'Invalid phone number');
            return;
        }

        // Choose URL scheme based on platform:
        // On iOS, 'telprompt:' shows a prompt; on Android, use 'tel:'.
        const urlScheme = Platform.OS === 'ios' ? 'telprompt:' : 'tel:';
        const phoneUrl = `${urlScheme}${cleanedPhoneNumber}`;

        // Attempt to open the URL directly and handle errors.
        Linking.openURL(phoneUrl).catch((error) => {
            console.error('Failed to open URL:', error);
            Alert.alert(
                'Error',
                'Could not initiate the phone call. Your device may not support this feature.',
                [{ text: 'OK' }]
            );
        });
    };

    const handleSharePress = async () => {
        if (!vehicle) {
            Alert.alert('Error', 'Vehicle details not available');
            return;
        }

        // Create a consistent deep link for the vehicle
        const deepLink = `https://car.2ndcar.in/api/vehicle/${selectedVehicleType}/${vehicle.id}`;

        const shareData = {
            title: vehicle.title || 'Vehicle Details',
            message: `Check out this ${vehicle?.title} on 2ndCar ${deepLink}`,
        };

        try {
            // Try native sharing
            const result = await Share.share({
                title: shareData.title,
                message: shareData.message,
            });

            if (result.action === Share.sharedAction) {
                if (result.activityType) {
                    console.log('Shared with activity type:', result.activityType);
                } else {
                    console.log('Shared successfully');
                }
            }
        } catch (error) {
            console.error('Error sharing:', error);
            // Fallback: Copy to clipboard
            try {
                await Clipboard.setStringAsync(shareData.message);
                Alert.alert(
                    'Success',
                    'Link copied to clipboard!',
                    [{ text: 'OK' }]
                );
            } catch (clipboardError) {
                console.error('Clipboard error:', clipboardError);
                Alert.alert(
                    'Error',
                    'Could not share or copy. Please try again.',
                    [{ text: 'OK' }]
                );
            }
        }
    };

    const handleMakeOffer = () => {
        setMakeOfferModalVisible(true);
    };

    const handleOfferSuccess = () => {
        // Offers are automatically updated in the Redux store
        // The OffersList component will re-render with new offers
    };

    // Sync offers when a new offer is successfully submitted
    useEffect(() => {
        if (submitSuccess) {
            // Re-fetch vehicle details to get the updated offers
            if (id && type) {
                dispatch(fetchVehicleDetail({ id: parseInt(id), type: type as VehicleType }));
            }
        }
    }, [submitSuccess, id, type, dispatch]);

    if (loading) {
        return (
            <View style={styles.loadingContainer}>
                <LoadingIndicator size={120} color1="#ff6b6b" color2="#4ecdc4" />
            </View>
        );
    }

    if (error || !vehicle)
        return <ThemedText>Error: {error}</ThemedText>;

    return (
        <View style={styles.container}>
            {/* Header Overlay */}
            <View style={styles.header}>
                <View style={styles.headerActions}>
                    {/* <TouchableOpacity style={styles.iconButton}>
                        <Ionicons name="heart-outline" size={24} color={COLORS.white} />
                    </TouchableOpacity> */}
                    <TouchableOpacity style={styles.iconButton} onPress={handleSharePress}>
                        <Ionicons name="share-social-outline" size={24} color={COLORS.white} />
                    </TouchableOpacity>
                </View>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
                {/* Main Image */}
                <Image
                    source={{
                        uri:
                            selectedImage ||
                            vehicle?.primary_image ||
                            'https://placeholder.com/300x200',
                    }}
                    style={styles.mainImage}
                />

                {/* Thumbnail Images */}
                <ScrollView
                    horizontal
                    style={styles.thumbnailContainer}
                    showsHorizontalScrollIndicator={false}
                    contentContainerStyle={styles.thumbnailContentContainer}
                >
                    {vehicle.images.map((image, index) => (
                        <TouchableOpacity key={index} onPress={() => setSelectedImage(image)}>
                            <Image
                                source={{ uri: image }}
                                style={[
                                    styles.thumbnail,
                                    selectedImage === image && styles.selectedThumbnail,
                                ]}
                                resizeMode="cover"
                            />
                        </TouchableOpacity>
                    ))}
                </ScrollView>

                {/* Vehicle Info */}
                <View style={styles.infoContainer}>
                    <View style={styles.titleRow}>
                        <View style={styles.titleContainer}>
                            <ThemedText style={styles.title}>{vehicle.title}</ThemedText>
                            <ThemedText style={styles.variant}>{vehicle.variant}</ThemedText>
                        </View>
                    </View>

                    {/* Price and Actions */}
                    <View style={styles.actionContainer}>
                        <View style={styles.priceContainer}>
                            <ThemedText style={styles.price}>
                                ₹ {parseInt(vehicle.price).toLocaleString('en-IN')}
                            </ThemedText>
                        </View>
                        <TouchableOpacity style={styles.actionButton} onPress={handleChatPress}>
                            <Ionicons
                                name="chatbubble-ellipses-outline"
                                size={20}
                                color={COLORS.white}
                            />
                            <ThemedText style={styles.buttonText}>Chat</ThemedText>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.actionButton} onPress={handleCallPress}>
                            <Ionicons name="call-outline" size={20} color={COLORS.white} />
                            <ThemedText style={styles.buttonText}>Call</ThemedText>
                        </TouchableOpacity>
                    </View>

                    {/* Offers Section */}
                    <OffersList
                        offers={offers}
                        onPlaceOffer={handleMakeOffer}
                    />

                    {/* Overview Section */}
                    <View style={styles.section}>
                        <TouchableOpacity
                            style={styles.sectionHeader}
                            onPress={() => setIsOverviewExpanded(!isOverviewExpanded)}
                        >
                            <ThemedText style={styles.sectionTitle}>Vehicle Overview</ThemedText>
                            <Ionicons
                                name={isOverviewExpanded ? 'chevron-up' : 'chevron-down'}
                                size={24}
                                color={COLORS.text.secondary}
                            />
                        </TouchableOpacity>

                        {isOverviewExpanded && (
                            <View style={styles.overviewGrid}>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="car-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Fuel Type: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.fuel_type}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="speedometer-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>KMs Driven: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.kilometers_driven} KM</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="settings-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Transmission: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.transmission}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="person-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>No. of Owners: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle.ownership}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="calendar-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Year: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.year}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                <View style={styles.overviewItem}>
                                    <Ionicons name="location-outline" size={24} color={COLORS.text.secondary} />
                                    <View style={styles.overviewItemText}>
                                        <ThemedText style={styles.overviewRow}>
                                            <ThemedText style={styles.overviewLabel}>Location: </ThemedText>
                                            <ThemedText style={styles.overviewValue}>{vehicle?.location}</ThemedText>
                                        </ThemedText>
                                    </View>
                                </View>
                                {vehicle?.insurance_type && (
                                    <View style={styles.overviewItem}>
                                        <Ionicons name="shield-checkmark-outline" size={24} color={COLORS.text.secondary} />
                                        <View style={styles.overviewItemText}>
                                            <ThemedText style={styles.overviewRow}>
                                                <ThemedText style={styles.overviewLabel}>Insurance: </ThemedText>
                                                <ThemedText style={styles.overviewValue}>
                                                    {getInsuranceDisplayName(vehicle.insurance_type)}
                                                </ThemedText>
                                            </ThemedText>
                                        </View>
                                    </View>
                                )}
                            </View>
                        )}
                    </View>

                    {/* Description Section */}
                    <View style={styles.section}>
                        <TouchableOpacity
                            style={styles.sectionHeader}
                            onPress={() => setIsDescriptionExpanded(!isDescriptionExpanded)}
                        >
                            <ThemedText style={styles.sectionTitle}>Description</ThemedText>
                            <Ionicons
                                name={isDescriptionExpanded ? 'chevron-up' : 'chevron-down'}
                                size={24}
                                color={COLORS.text.secondary}
                            />
                        </TouchableOpacity>
                        {isDescriptionExpanded && (
                            <ThemedText style={styles.descriptionText}>
                                {vehicle?.description || 'No description available'}
                            </ThemedText>
                        )}
                    </View>
                </View>
            </ScrollView>

            {/* Make Offer Modal */}
            {vehicle && (
                <MakeOfferModal
                    visible={makeOfferModalVisible}
                    onClose={() => setMakeOfferModalVisible(false)}
                    vehicleId={vehicle.id}
                    vehicleType={type || 'car'}
                    vehiclePrice={parseInt(vehicle.price)}
                    onOfferSuccess={handleOfferSuccess}
                />
            )}
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        backgroundColor: COLORS.background,
        position: 'relative',
    },
    header: {
        position: 'absolute',
        top: SPACING.md,
        right: SPACING.md,
        zIndex: 10,
        flexDirection: 'row',
    },
    headerActions: {
        flexDirection: 'row',
        gap: SPACING.sm,
    },
    iconButton: {
        padding: SPACING.xs,
        backgroundColor: 'rgba(0,0,0,0.5)',
        borderRadius: 20,
        marginLeft: SPACING.sm,
    },
    mainImage: {
        width: width,
        height: width * 0.75,
    },
    thumbnailContainer: {
        backgroundColor: COLORS.background,
        paddingHorizontal: SPACING.sm,
        paddingVertical: SPACING.xs,
        marginVertical: SPACING.sm,
        minHeight: 80,
    },
    thumbnailContentContainer: {
        alignItems: 'center',
    },
    thumbnail: {
        width: 60,
        height: 60,
        marginRight: SPACING.xs,
        borderRadius: 4,
    },
    selectedThumbnail: {
        borderWidth: 2,
        borderColor: COLORS.primary,
    },
    infoContainer: {
        padding: SPACING.md,
    },
    titleRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: SPACING.md,
    },
    titleContainer: {
        flex: 1,
    },
    title: {
        fontSize: 20,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    variant: {
        color: COLORS.text.secondary,
        marginTop: SPACING.xs,
    },
    actionContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: SPACING.md,
    },
    priceContainer: {
        flex: 1,
    },
    price: {
        fontSize: 24,
        fontWeight: 'bold',
        color: COLORS.primary,
    },
    actionButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: COLORS.primary,
        padding: SPACING.sm,
        borderRadius: 8,
        marginLeft: SPACING.sm,
    },
    buttonText: {
        color: COLORS.white,
        marginLeft: SPACING.xs,
        fontSize: 16,
        fontWeight: 'bold',
    },
    bidSection: {
        marginVertical: SPACING.md,
        width: '100%',
    },
    bidSectionTitle: {
        fontSize: 16,
        marginBottom: SPACING.sm,
        color: COLORS.text.primary,
    },
    bidContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: SPACING.md,
    },
    bidInfoContainer: {
        flexDirection: 'row',
        flex: 1,
        borderWidth: 0.4,
        borderRadius: 8,
        borderColor: '#1A2B50',
        overflow: 'hidden', // Ensures the bidNumberContainer respects the border radius
    },
    bidNumberContainer: {
        width: 50,
        height: 50,
        backgroundColor: '#1A2B50',
        justifyContent: 'center',
        alignItems: 'center',
    },
    bidNumber: {
        color: COLORS.white,
        fontSize: 24,
        fontWeight: 'bold',
    },
    bidAmountContainer: {
        flex: 1,
        paddingHorizontal: SPACING.md,
        justifyContent: 'center',
    },
    bidAmount: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    makeOfferButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: '#FF5722',
        paddingVertical: 14,
        paddingHorizontal: SPACING.lg,
        borderRadius: 8,
        gap: SPACING.xs,
    },
    section: {
        marginVertical: SPACING.md,
        backgroundColor: COLORS.white,
        borderRadius: 12,
        padding: SPACING.md,
    },
    sectionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        color: COLORS.text.primary,
    },
    overviewGrid: {
        marginTop: SPACING.md,
        gap: SPACING.md,
    },
    overviewItem: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: SPACING.md,
    },
    overviewItemText: {
        flex: 1,
    },
    overviewRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    overviewLabel: {
        color: COLORS.text.secondary,
        fontSize: 14,
    },
    overviewValue: {
        color: COLORS.text.primary,
        fontSize: 16,
    },
    descriptionText: {
        marginTop: SPACING.md,
        fontSize: 16,
        color: COLORS.text.secondary,
        lineHeight: 24,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: COLORS.background,
    },
});
